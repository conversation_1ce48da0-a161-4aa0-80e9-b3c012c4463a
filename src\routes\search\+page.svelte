<script lang="ts">
	import { Search } from '@lucide/svelte';
	import { onMount } from 'svelte';
	import { fly } from 'svelte/transition';
	import { posts } from '$lib/data/posts';
	import { categories } from '$lib/data/categories';
	import PostGrid from '$lib/components/PostGrid.svelte';

	let searchTerm = '';
	let searchResults = [];
	let selectedFilter = 'all';
	let recentSearches = ['极简主义', '咖啡', '日式家居', '健康饮食'];

	const filters = [
		{ id: 'all', label: '综合' },
		{ id: 'posts', label: '笔记' },
		{ id: 'users', label: '用户' },
		{ id: 'topics', label: '话题' }
	];

	function handleSearch() {
		if (searchTerm.trim()) {
			// Save to recent searches if not already there
			if (!recentSearches.includes(searchTerm)) {
				recentSearches = [searchTerm, ...recentSearches].slice(0, 5);
			}

			// Perform search
			searchResults = posts.filter(
				(post) =>
					post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
					post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
					post.categories.some((cat) => cat.toLowerCase().includes(searchTerm.toLowerCase()))
			);
		}
	}

	function clearSearch() {
		searchTerm = '';
		searchResults = [];
	}

	function useRecentSearch(term) {
		searchTerm = term;
		handleSearch();
	}

	onMount(() => {
		// Focus search input on page load
		const searchInput = document.getElementById('search-input');
		if (searchInput) {
			searchInput.focus();
		}
	});
</script>

<svelte:head>
	<title>搜索 - 小红书</title>
	<meta name="description" content="搜索感兴趣的内容、用户和话题" />
</svelte:head>

<div class="container-custom py-4" in:fly={{ y: 20, duration: 400 }}>
	<div class="search-container">
		<div class="relative">
			<input
				id="search-input"
				type="text"
				bind:value={searchTerm}
				placeholder="搜索感兴趣的内容、用户和话题"
				class="focus:ring-primary-500 w-full rounded-full bg-gray-100 px-4 py-3 pl-12 text-base focus:ring-2 focus:outline-none"
				on:keydown={(e) => e.key === 'Enter' && handleSearch()}
			/>
			<Search size={20} class="absolute top-1/2 left-4 -translate-y-1/2 transform text-gray-500" />

			{#if searchTerm}
				<button
					class="absolute top-1/2 right-4 -translate-y-1/2 transform text-gray-500"
					on:click={clearSearch}
				>
					✕
				</button>
			{/if}
		</div>

		<button class="btn btn-primary ml-2 hidden sm:inline-flex" on:click={handleSearch}>
			搜索
		</button>
	</div>

	{#if !searchResults.length && !searchTerm}
		<div class="mt-6">
			<!-- Recent searches -->
			{#if recentSearches.length > 0}
				<div class="mb-8">
					<h3 class="mb-3 text-base font-medium">最近搜索</h3>
					<div class="flex flex-wrap gap-2">
						{#each recentSearches as term}
							<button
								class="rounded-full bg-gray-100 px-4 py-2 text-sm hover:bg-gray-200"
								on:click={() => useRecentSearch(term)}
							>
								{term}
							</button>
						{/each}
					</div>
				</div>
			{/if}

			<!-- Popular categories -->
			<div>
				<h3 class="mb-3 text-base font-medium">热门分类</h3>
				<div class="grid grid-cols-2 gap-4 sm:grid-cols-4">
					{#each categories as category}
						<button
							class="rounded-lg bg-gray-100 p-4 text-center hover:bg-gray-200"
							on:click={() => {
								searchTerm = category;
								handleSearch();
							}}
						>
							#{category}
						</button>
					{/each}
				</div>
			</div>
		</div>
	{:else if searchResults.length > 0}
		<!-- Search filters -->
		<div class="mt-4 border-b border-gray-200">
			<div class="flex space-x-6">
				{#each filters as filter}
					<button
						class="relative px-1 py-3 text-gray-600 {selectedFilter === filter.id
							? 'text-primary-500 font-semibold'
							: 'hover:text-gray-800'}"
						on:click={() => (selectedFilter = filter.id)}
					>
						{filter.label}
						{#if selectedFilter === filter.id}
							<div class="bg-primary-500 absolute bottom-0 left-0 h-0.5 w-full"></div>
						{/if}
					</button>
				{/each}
			</div>
		</div>

		<!-- Search results -->
		<div class="mt-6">
			<h3 class="mb-3 text-base font-medium">找到 {searchResults.length} 条结果</h3>
			<PostGrid posts={searchResults} />
		</div>
	{:else if searchTerm}
		<!-- No results -->
		<div class="py-20 text-center">
			<p class="mb-4 text-lg text-gray-600">没有找到与 "{searchTerm}" 相关的结果</p>
			<p class="text-gray-500">尝试其他关键词或浏览热门分类</p>

			<div class="mt-8">
				<h3 class="mb-3 text-base font-medium">热门分类</h3>
				<div class="flex flex-wrap justify-center gap-3">
					{#each categories.slice(0, 8) as category}
						<button
							class="rounded-full bg-gray-100 px-4 py-2 text-sm hover:bg-gray-200"
							on:click={() => {
								searchTerm = category;
								handleSearch();
							}}
						>
							#{category}
						</button>
					{/each}
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.search-container {
		display: flex;
		align-items: center;
		width: 100%;
	}
</style>
