<script lang="ts">
	import { onMount } from 'svelte';
	import PostGrid from '$lib/components/PostGrid.svelte';
	import CategoryBar from '$lib/components/CategoryBar.svelte';
	import { posts } from '$lib/data/posts';
	import { categories } from '$lib/data/categories';
	import { fly } from 'svelte/transition';

	let selectedCategory = 'all';
	let filteredPosts = [...posts];

	function filterPostsByCategory(category: string) {
		selectedCategory = category;
		if (category === 'all') {
			filteredPosts = [...posts];
		} else {
			filteredPosts = posts.filter((post) => post.categories.includes(category));
		}
	}

	onMount(() => {
		// Any initialization code here
	});
</script>

<svelte:head>
	<title>小红书 - 标记我的生活</title>
	<meta name="description" content="发现和分享生活中的美好" />
</svelte:head>

<div class="container-custom py-4" in:fly={{ y: 20, duration: 400 }}>
	<CategoryBar {categories} {selectedCategory} on:select={(e) => filterPostsByCategory(e.detail)} />

	<section class="mt-6">
		<PostGrid posts={filteredPosts} />
	</section>
</div>
