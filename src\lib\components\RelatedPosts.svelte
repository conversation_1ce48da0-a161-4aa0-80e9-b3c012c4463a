<script lang="ts">
    import { goto } from '$app/navigation';
    
    export let posts = [];
    
    function viewPost(postId) {
      goto(`/post/${postId}`);
    }
  </script>
  
  <div class="grid grid-cols-2 sm:grid-cols-4 gap-3">
    {#each posts as post}
      <div 
        class="card cursor-pointer h-40 relative overflow-hidden"
        on:click={() => viewPost(post.id)}
      >
        <img 
          src={post.imageUrl} 
          alt={post.title} 
          class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
        />
        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
          <p class="text-white text-xs font-medium line-clamp-2">{post.title}</p>
        </div>
      </div>
    {/each}
  </div>