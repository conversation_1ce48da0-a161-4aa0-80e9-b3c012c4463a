/* Write your global styles here, in PostCSS syntax */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	color-scheme: light dark;
	font-synthesis: none;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

html,
body {
	height: 100%;
	margin: 0;
	padding: 0;
	background-color: #fafafa;
	color: #333;
	font-family:
		'Inter',
		-apple-system,
		BlinkMacSystemFont,
		'Segoe UI',
		Roboto,
		Oxygen,
		Ubuntu,
		Cantarell,
		'Open Sans',
		'Helvetica Neue',
		sans-serif;
}

/* Card styles with rounded corners and shadows */
.card {
	@apply shadow-card overflow-hidden rounded-lg bg-white transition-shadow duration-300;
}

.card:hover {
	@apply shadow-card-hover;
}

/* Container with proper padding for all device sizes */
.container-custom {
	@apply mx-auto max-w-7xl px-4 md:px-6 lg:px-8;
}

/* Button styles with consistent design */
.btn {
	@apply inline-flex items-center justify-center rounded-full px-4 py-2 font-medium transition-colors duration-200;
}

.btn-primary {
	@apply bg-primary-500 hover:bg-primary-600 text-white;
}

.btn-secondary {
	@apply bg-secondary-500 hover:bg-secondary-600 text-white;
}

.btn-outline {
	@apply border border-gray-300 bg-transparent hover:bg-gray-50;
}

.btn-icon {
	@apply rounded-full p-2;
}

/* Hide scrollbar but allow scrolling */
.no-scrollbar {
	-ms-overflow-style: none;
	scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
	display: none;
}

/* Fade in animation class */
.fade-in {
	@apply animate-fade-in;
}

/* Masonry grid styles */
.masonry-grid {
	@apply gap-4;
	column-count: 2;
	column-gap: 1rem;
}

.masonry-grid > div {
	break-inside: avoid;
	margin-bottom: 1rem;
}

@media (min-width: 640px) {
	.masonry-grid {
		column-count: 3;
	}
}

@media (min-width: 1024px) {
	.masonry-grid {
		column-count: 4;
	}
}

@media (min-width: 1280px) {
	.masonry-grid {
		column-count: 5;
	}
}
