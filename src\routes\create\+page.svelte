<script lang="ts">
	import { onMount } from 'svelte';
	import { Image, X, Plus, MapPin } from '@lucide/svelte';
	import { fly } from 'svelte/transition';
	import { categories } from '$lib/data/categories';

	let title = '';
	let content = '';
	let selectedCategories = [];
	let imagePreview = null;
	let imageFile = null;

	function handleImageSelect(event) {
		const file = event.target.files[0];
		if (file) {
			imageFile = file;
			const reader = new FileReader();
			reader.onload = (e) => {
				imagePreview = e.target.result;
			};
			reader.readAsDataURL(file);
		}
	}

	function removeImage() {
		imagePreview = null;
		imageFile = null;
	}

	function toggleCategory(category) {
		if (selectedCategories.includes(category)) {
			selectedCategories = selectedCategories.filter((c) => c !== category);
		} else {
			if (selectedCategories.length < 3) {
				selectedCategories = [...selectedCategories, category];
			}
		}
	}

	function handleSubmit() {
		if (!title || !content || !imagePreview) {
			alert('请填写完整信息并上传图片');
			return;
		}

		// This would typically make an API call to save the post
		alert('发布成功！');

		// Reset form
		title = '';
		content = '';
		selectedCategories = [];
		imagePreview = null;
		imageFile = null;
	}

	onMount(() => {
		// Focus title input on page load
		const titleInput = document.getElementById('post-title');
		if (titleInput) {
			titleInput.focus();
		}
	});
</script>

<svelte:head>
	<title>发布笔记 - 小红书</title>
	<meta name="description" content="创建并分享你的笔记" />
</svelte:head>

<div class="container-custom max-w-3xl py-4" in:fly={{ y: 20, duration: 400 }}>
	<h1 class="mb-6 text-xl font-bold">创建新笔记</h1>

	<form class="space-y-6" on:submit|preventDefault={handleSubmit}>
		<!-- Title -->
		<div>
			<label for="post-title" class="mb-1 block text-sm font-medium text-gray-700">标题</label>
			<input
				id="post-title"
				type="text"
				bind:value={title}
				placeholder="添加标题（最多 30 字）"
				maxlength="30"
				class="focus:ring-primary-500 w-full rounded-lg border border-gray-300 p-3 focus:ring-2 focus:outline-none"
				required
			/>
		</div>

		<!-- Image Upload -->
		<div>
			<label class="mb-1 block text-sm font-medium text-gray-700">封面图片</label>

			{#if imagePreview}
				<div class="relative overflow-hidden rounded-lg">
					<img src={imagePreview} alt="Preview" class="h-64 w-full object-cover" />
					<button
						type="button"
						class="absolute top-2 right-2 rounded-full bg-black/50 p-1 text-white"
						on:click={removeImage}
					>
						<X size={16} />
					</button>
				</div>
			{:else}
				<label
					class="block h-64 w-full cursor-pointer rounded-lg border-2 border-dashed border-gray-300 hover:bg-gray-50"
				>
					<div class="flex h-full flex-col items-center justify-center">
						<Image size={32} class="mb-4 text-gray-400" />
						<span class="text-gray-500">点击上传图片</span>
						<span class="mt-1 text-xs text-gray-400">支持 JPG、PNG 格式</span>
					</div>
					<input type="file" accept="image/*" class="hidden" on:change={handleImageSelect} />
				</label>
			{/if}
		</div>

		<!-- Content -->
		<div>
			<label for="post-content" class="mb-1 block text-sm font-medium text-gray-700">内容</label>
			<textarea
				id="post-content"
				bind:value={content}
				placeholder="分享你的想法、经验或创意..."
				class="focus:ring-primary-500 min-h-[200px] w-full rounded-lg border border-gray-300 p-3 focus:ring-2 focus:outline-none"
				required
			></textarea>
		</div>

		<!-- Categories -->
		<div>
			<label class="mb-1 block text-sm font-medium text-gray-700">分类（最多选择 3 个）</label>
			<div class="flex flex-wrap gap-2">
				{#each categories as category}
					<button
						type="button"
						class="rounded-full px-3 py-1.5 text-sm {selectedCategories.includes(category)
							? 'bg-primary-500 text-white'
							: 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
						on:click={() => toggleCategory(category)}
					>
						{category}
					</button>
				{/each}
			</div>
		</div>

		<!-- Location (optional) -->
		<div>
			<button type="button" class="hover:text-primary-500 flex items-center text-gray-500">
				<MapPin size={16} class="mr-1" />
				<span>添加位置</span>
			</button>
		</div>

		<!-- Submit button -->
		<div class="pt-4">
			<button
				type="submit"
				class="btn btn-primary w-full py-3"
				disabled={!title || !content || !imagePreview}
			>
				发布笔记
			</button>
		</div>
	</form>
</div>
