<script lang="ts">
    export let user;
    export let size = 'md'; // sm, md, lg
    
    // Size classes mapping
    const sizeClasses = {
      sm: 'w-6 h-6',
      md: 'w-10 h-10',
      lg: 'w-14 h-14'
    };
    
    // Font size for initials
    const fontSizeClasses = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base'
    };
  </script>
  
  <div class="relative inline-block">
    {#if user.avatar}
      <img 
        src={user.avatar} 
        alt={user.name} 
        class="{sizeClasses[size]} rounded-full object-cover border border-gray-200" 
      />
    {:else}
      <!-- Fallback to initials if no avatar -->
      <div class="{sizeClasses[size]} rounded-full bg-gray-200 flex items-center justify-center">
        <span class="{fontSizeClasses[size]} font-medium text-gray-700">
          {user.name.charAt(0)}
        </span>
      </div>
    {/if}
  </div>