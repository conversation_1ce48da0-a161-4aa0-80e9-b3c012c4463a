<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { slide } from 'svelte/transition';
    
    export let categories = [];
    export let selectedCategory = 'all';
    
    const dispatch = createEventDispatcher();
    
    function selectCategory(category) {
      dispatch('select', category);
    }
  </script>
  
  <div class="overflow-x-auto no-scrollbar py-2">
    <div class="flex space-x-2 min-w-max">
      <button 
        class="px-4 py-1.5 rounded-full text-sm whitespace-nowrap transition-colors duration-200 {selectedCategory === 'all' 
          ? 'bg-primary-500 text-white' 
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
        on:click={() => selectCategory('all')}
        in:slide
      >
        全部
      </button>
      
      {#each categories as category}
        <button 
          class="px-4 py-1.5 rounded-full text-sm whitespace-nowrap transition-colors duration-200 {selectedCategory === category 
            ? 'bg-primary-500 text-white' 
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
          on:click={() => selectCategory(category)}
          in:slide
        >
          {category}
        </button>
      {/each}
    </div>
  </div>