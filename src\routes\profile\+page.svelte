<script lang="ts">
    import { onMount } from 'svelte';
    import { fly } from 'svelte/transition';
    import { posts } from '$lib/data/posts';
    import ProfileHeader from '$lib/components/ProfileHeader.svelte';
    import ProfileTabs from '$lib/components/ProfileTabs.svelte';
    import PostGrid from '$lib/components/PostGrid.svelte';
    
    const userPosts = posts.slice(0, 12);
    const likedPosts = posts.slice(12, 18);
    const savedPosts = posts.slice(5, 11);
    
    let activeTab = 'posts';
    let displayedPosts = userPosts;
    
    function handleTabChange(tab: string) {
      activeTab = tab;
      
      switch (tab) {
        case 'posts':
          displayedPosts = userPosts;
          break;
        case 'likes':
          displayedPosts = likedPosts;
          break;
        case 'collections':
          displayedPosts = savedPosts;
          break;
        default:
          displayedPosts = userPosts;
      }
    }
    
    const user = {
      id: '1',
      name: '晨曦的风',
      avatar: 'https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=150', 
      followers: 1289,
      following: 356,
      bio: '分享美食、旅行和生活中的小确幸 ✨'
    };
    
    onMount(() => {
      // Initialize the page
    });
  </script>
  
  <svelte:head>
    <title>{user.name} - 个人主页 - 小红书</title>
    <meta name="description" content={`${user.name}的个人主页 - ${user.bio}`} />
  </svelte:head>
  
  <div class="container-custom" in:fly={{ y: 20, duration: 400 }}>
    <ProfileHeader {user} />
    
    <ProfileTabs 
      activeTab={activeTab} 
      onTabChange={handleTabChange} 
      counts={{
        posts: userPosts.length,
        likes: likedPosts.length,
        collections: savedPosts.length
      }}
    />
    
    <section class="py-4">
      <PostGrid posts={displayedPosts} />
    </section>
  </div>