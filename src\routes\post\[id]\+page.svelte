<script lang="ts">
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { posts } from '$lib/data/posts';
	import { Heart, Bookmark, Share2, MessageSquare, ChevronLeft } from '@lucide/svelte';
	import RelatedPosts from '$lib/components/RelatedPosts.svelte';
	import UserAvatar from '$lib/components/UserAvatar.svelte';
	import PostComments from '$lib/components/PostComments.svelte';
	import { fly, fade } from 'svelte/transition';
	import { goto } from '$app/navigation';

	const postId = $page.params.id;
	const post = posts.find((p) => p.id === postId);

	let isLiked = false;
	let isSaved = false;
	let relatedPosts = posts.filter((p) => p.id !== postId).slice(0, 4);

	function toggleLike() {
		isLiked = !isLiked;
	}

	function toggleSave() {
		isSaved = !isSaved;
	}

	function goBack() {
		goto('/');
	}

	// Generate the rich text paragraphs from the content string
	let contentParagraphs = post?.content.split('\n').filter((p) => p.trim() !== '') || [];

	onMount(() => {
		// Scroll to top when the page loads
		window.scrollTo(0, 0);
	});
</script>

<svelte:head>
	<title>{post?.title || '文章详情'} - 小红书</title>
	<meta name="description" content={post?.content.substring(0, 160) || '文章详情页'} />
</svelte:head>

{#if post}
	<div class="container-custom max-w-3xl py-4" in:fly={{ y: 20, duration: 400 }}>
		<!-- Back button -->
		<button class="hover:text-primary-500 mb-4 flex items-center text-gray-600" on:click={goBack}>
			<ChevronLeft size={20} />
			<span>返回</span>
		</button>

		<!-- Post header -->
		<div class="mb-4 flex items-center space-x-3">
			<UserAvatar user={post.user} size="lg" />
			<div>
				<h4 class="font-semibold">{post.user.name}</h4>
				<p class="text-xs text-gray-500">{post.date}</p>
			</div>
			<button class="btn btn-outline ml-auto rounded-full text-sm">关注</button>
		</div>

		<!-- Post content -->
		<div class="post-content mb-6">
			<h1 class="mb-4 text-2xl font-bold">{post.title}</h1>

			<div class="mb-6 overflow-hidden rounded-xl">
				<img
					src={post.imageUrl}
					alt={post.title}
					class="w-full object-cover"
					style="max-height: 500px;"
					transition:fade={{ duration: 300 }}
				/>
			</div>

			<div class="leading-relaxed text-gray-800">
				{#each contentParagraphs as paragraph}
					<p class="mb-4">{paragraph}</p>
				{/each}
			</div>

			<!-- Tags -->
			<div class="mt-4 flex flex-wrap gap-2">
				{#each post.categories as category}
					<span class="rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-600">
						#{category}
					</span>
				{/each}
			</div>
		</div>

		<!-- Action bar -->
		<div class="flex justify-between border-t border-b border-gray-100 py-4">
			<button class="flex items-center gap-1" on:click={toggleLike}>
				<Heart
					size={20}
					class={isLiked
						? 'fill-primary-500 text-primary-500 animate-like-bounce'
						: 'text-gray-600'}
				/>
				<span>{post.likes + (isLiked ? 1 : 0)}</span>
			</button>

			<button class="flex items-center gap-1">
				<MessageSquare size={20} class="text-gray-600" />
				<span>{post.comments.length}</span>
			</button>

			<button class="flex items-center gap-1" on:click={toggleSave}>
				<Bookmark size={20} class={isSaved ? 'fill-accent-500 text-accent-500' : 'text-gray-600'} />
				<span>收藏</span>
			</button>

			<button class="flex items-center gap-1">
				<Share2 size={20} class="text-gray-600" />
				<span>分享</span>
			</button>
		</div>

		<!-- Comments section -->
		<section class="my-6">
			<h3 class="mb-4 text-lg font-semibold">评论 ({post.comments.length})</h3>
			<PostComments comments={post.comments} />
		</section>

		<!-- Related posts -->
		<section class="my-8">
			<h3 class="mb-4 text-lg font-semibold">相关推荐</h3>
			<RelatedPosts posts={relatedPosts} />
		</section>
	</div>
{:else}
	<div class="container-custom py-20 text-center">
		<h2 class="text-xl text-gray-600">文章不存在或已被删除</h2>
		<button class="btn btn-primary mt-4" on:click={goBack}> 返回首页 </button>
	</div>
{/if}

<style>
	.post-content {
		font-size: 16px;
		line-height: 1.6;
	}
</style>
