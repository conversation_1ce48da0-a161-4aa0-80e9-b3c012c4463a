<script lang="ts">
    import { fade } from 'svelte/transition';
    import UserAvatar from './UserAvatar.svelte';
    
    export let comments = [];
    
    let commentText = '';
    
    function addComment() {
      if (commentText.trim()) {
        // This would typically call an API to save the comment
        commentText = '';
      }
    }
  </script>
  
  <div class="space-y-4">
    <!-- Comment input -->
    <div class="flex items-start space-x-3 mb-6">
      <UserAvatar 
        user={{ name: '我', avatar: null }} 
        size="md" 
      />
      
      <div class="flex-1">
        <textarea
          bind:value={commentText}
          placeholder="写下你的评论..."
          class="w-full p-3 border border-gray-200 rounded-lg text-sm min-h-[80px] focus:outline-none focus:ring-2 focus:ring-primary-300"
        ></textarea>
        
        <div class="flex justify-end mt-2">
          <button 
            class="btn btn-primary text-sm py-1 px-4 {!commentText.trim() ? 'opacity-50 cursor-not-allowed' : ''}"
            disabled={!commentText.trim()}
            on:click={addComment}
          >
            发布评论
          </button>
        </div>
      </div>
    </div>
    
    <!-- Comments list -->
    {#if comments.length > 0}
      <div class="space-y-4">
        {#each comments as comment, index}
          <div class="flex space-x-3" in:fade={{ delay: index * 100, duration: 300 }}>
            <UserAvatar user={comment.user} size="md" />
            
            <div class="flex-1">
              <div class="bg-gray-50 p-3 rounded-lg">
                <div class="flex justify-between items-start">
                  <h4 class="font-medium text-sm">{comment.user.name}</h4>
                  <span class="text-xs text-gray-500">{comment.date}</span>
                </div>
                
                <p class="text-gray-800 text-sm mt-1">{comment.text}</p>
              </div>
              
              <div class="flex space-x-4 mt-1 ml-2 text-xs text-gray-500">
                <button class="hover:text-primary-500">回复</button>
                <button class="hover:text-primary-500">点赞</button>
              </div>
              
              {#if comment.replies && comment.replies.length > 0}
                <div class="ml-6 mt-2 space-y-3">
                  {#each comment.replies as reply}
                    <div class="flex space-x-2">
                      <UserAvatar user={reply.user} size="sm" />
                      
                      <div class="flex-1">
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <div class="flex justify-between items-start">
                            <h5 class="font-medium text-xs">{reply.user.name}</h5>
                            <span class="text-xs text-gray-500">{reply.date}</span>
                          </div>
                          
                          <p class="text-gray-800 text-xs mt-1">{reply.text}</p>
                        </div>
                        
                        <div class="flex space-x-4 mt-0.5 ml-2 text-xs text-gray-500">
                          <button class="hover:text-primary-500">回复</button>
                          <button class="hover:text-primary-500">点赞</button>
                        </div>
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          </div>
        {/each}
      </div>
    {:else}
      <div class="text-center py-8 text-gray-500">
        <p>暂无评论，成为第一个评论的人吧！</p>
      </div>
    {/if}
  </div>