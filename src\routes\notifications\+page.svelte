<script lang="ts">
	import { onMount } from 'svelte';
	import { fly } from 'svelte/transition';
	import UserAvatar from '$lib/components/UserAvatar.svelte';
	import { Heart, MessageSquare, UserPlus } from '@lucide/svelte';

	let activeTab = 'all';

	const tabs = [
		{ id: 'all', label: '全部' },
		{ id: 'likes', label: '赞', icon: Heart },
		{ id: 'comments', label: '评论', icon: MessageSquare },
		{ id: 'follows', label: '关注', icon: UserPlus }
	];

	// Sample notifications data
	let notifications = [
		{
			id: 'n1',
			type: 'likes',
			user: {
				id: '201',
				name: '烘焙小达人',
				avatar:
					'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150'
			},
			content: '赞了你的笔记',
			target: '周末自制草莓蛋糕！',
			time: '10分钟前',
			read: false
		},
		{
			id: 'n2',
			type: 'comments',
			user: {
				id: '203',
				name: '樱花控',
				avatar:
					'https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=150'
			},
			content: '评论了你的笔记',
			target: '京都旅行穿搭日记',
			comment: '太美了！请问是哪家和服店呢？我下个月也要去京都！',
			time: '1小时前',
			read: false
		},
		{
			id: 'n3',
			type: 'follows',
			user: {
				id: '205',
				name: '向往简单',
				avatar:
					'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=150'
			},
			content: '关注了你',
			time: '3小时前',
			read: true
		},
		{
			id: 'n4',
			type: 'likes',
			user: {
				id: '207',
				name: '冥想爱好者',
				avatar:
					'https://images.pexels.com/photos/1261731/pexels-photo-1261731.jpeg?auto=compress&cs=tinysrgb&w=150'
			},
			content: '赞了你的笔记',
			target: '为什么你应该学习冥想',
			time: '昨天',
			read: true
		}
	];

	$: filteredNotifications =
		activeTab === 'all' ? notifications : notifications.filter((n) => n.type === activeTab);

	function markAsRead(id) {
		// This would typically make an API call to mark notification as read
		// In this demo, we're just setting a local state
		const index = notifications.findIndex((n) => n.id === id);
		if (index !== -1) {
			notifications[index].read = true;
			notifications = [...notifications];
		}
	}

	onMount(() => {
		// Any initialization code here
	});
</script>

<svelte:head>
	<title>通知 - 小红书</title>
	<meta name="description" content="查看互动和通知" />
</svelte:head>

<div class="container-custom py-4" in:fly={{ y: 20, duration: 400 }}>
	<h1 class="mb-6 text-xl font-bold">通知</h1>

	<!-- Tabs -->
	<div class="mb-4 border-b border-gray-200">
		<div class="flex space-x-6">
			{#each tabs as tab}
				<button
					class="relative px-1 py-3 text-gray-600 {activeTab === tab.id
						? 'text-primary-500 font-semibold'
						: 'hover:text-gray-800'}"
					on:click={() => (activeTab = tab.id)}
				>
					<div class="flex items-center">
						{#if tab.icon}
							<svelte:component this={tab.icon} size={16} class="mr-1" />
						{/if}
						<span>{tab.label}</span>
					</div>

					{#if activeTab === tab.id}
						<div class="bg-primary-500 absolute bottom-0 left-0 h-0.5 w-full"></div>
					{/if}
				</button>
			{/each}
		</div>
	</div>

	<!-- Notifications list -->
	<div class="space-y-1">
		{#if filteredNotifications.length > 0}
			{#each filteredNotifications as notification (notification.id)}
				<div
					class="rounded-lg p-4 {notification.read
						? 'bg-white'
						: 'bg-primary-50'} transition-colors duration-200 hover:bg-gray-50"
					on:click={() => markAsRead(notification.id)}
					in:fly={{ y: 10, duration: 300 }}
				>
					<div class="flex items-start">
						<UserAvatar user={notification.user} size="md" />

						<div class="ml-3 flex-1">
							<div class="flex items-start justify-between">
								<div>
									<span class="font-medium">{notification.user.name}</span>
									<span class="text-gray-600"> {notification.content}</span>
									{#if notification.target}
										<span class="font-medium">「{notification.target}」</span>
									{/if}
								</div>
								<span class="text-xs text-gray-500">{notification.time}</span>
							</div>

							{#if notification.comment}
								<p class="mt-1 rounded bg-gray-50 p-2 text-sm text-gray-600">
									{notification.comment}
								</p>
							{/if}

							<div class="mt-2 flex justify-between">
								{#if notification.type === 'follows'}
									<button class="btn btn-outline px-3 py-1 text-xs">回访</button>
								{:else}
									<button class="text-primary-500 hover:text-primary-600 text-xs">查看详情</button>
								{/if}
							</div>
						</div>
					</div>
				</div>
			{/each}
		{:else}
			<div class="py-20 text-center text-gray-500">
				<p>暂无通知</p>
			</div>
		{/if}
	</div>
</div>
