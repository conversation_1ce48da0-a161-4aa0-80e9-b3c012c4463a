<script lang="ts">
	import { onMount } from 'svelte';
	import PostCard from './PostCard.svelte';
	import { slide } from 'svelte/transition';

	export let posts = [];

	// For the staggered effect when loading
	let visiblePosts = [];

	onMount(() => {
		// Stagger the post appearance for a more dynamic loading effect
		let delay = 0;
		const interval = setInterval(() => {
			if (visiblePosts.length < posts.length) {
				visiblePosts = [...visiblePosts, posts[visiblePosts.length]];
			} else {
				clearInterval(interval);
			}
		}, 50);

		return () => clearInterval(interval);
	});

	$: if (posts.length > 0 && visiblePosts.length === 0) {
		// Initialize with first batch of posts when posts change
		visiblePosts = posts.slice(0, Math.min(8, posts.length));
	}
</script>

<div class="masonry-grid">
	{#each visiblePosts as post, index (post.id)}
		<div>
			<PostCard {post} />
		</div>
	{/each}
</div>

{#if visiblePosts.length === 0}
	<div class="py-20 text-center text-gray-500">
		<p>没有找到相关内容</p>
	</div>
{/if}
