<script lang="ts">
	import "../app.postcss";
	import Header from '$lib/components/Header.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	
	let showHeader = true;
	let prevScrollPos = 0;
	
	onMount(() => {
	  const handleScroll = () => {
		const currentScrollPos = window.scrollY;
		showHeader = prevScrollPos > currentScrollPos || currentScrollPos < 50;
		prevScrollPos = currentScrollPos;
	  };
	  
	  window.addEventListener('scroll', handleScroll);
	  
	  return () => {
		window.removeEventListener('scroll', handleScroll);
	  };
	});
  
	$: isPostPage = $page.route.id === '/post/[id]';
  </script>
  
  <div class="flex flex-col min-h-screen">
	<Header {showHeader} />
	
	<main class="flex-1">
	  <slot />
	</main>
	
	<!-- Only show footer on pages other than individual post page -->
	{#if !isPostPage}
	  <Footer />
	{/if}
  </div>
  
  <style>
	main {
	  margin-top: 60px; /* Offset for fixed header */
	}
  </style>