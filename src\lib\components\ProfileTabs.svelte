<script lang="ts">
    export let activeTab = 'posts';
    export let onTabChange;
    export let counts = {
      posts: 0,
      likes: 0,
      collections: 0
    };
    
    const tabs = [
      { id: 'posts', label: '笔记', count: counts.posts },
      { id: 'collections', label: '收藏', count: counts.collections },
      { id: 'likes', label: '赞过', count: counts.likes }
    ];
  </script>
  
  <div class="border-b border-gray-200 mb-4">
    <div class="flex space-x-8">
      {#each tabs as tab}
        <button 
          class="py-3 px-1 text-gray-600 relative {activeTab === tab.id ? 'font-semibold text-primary-500' : 'hover:text-gray-800'}"
          on:click={() => onTabChange(tab.id)}
        >
          {tab.label} ({tab.count})
          {#if activeTab === tab.id}
            <div class="absolute bottom-0 left-0 w-full h-0.5 bg-primary-500"></div>
          {/if}
        </button>
      {/each}
    </div>
  </div>