<script lang="ts">
	import { Use<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>C<PERSON> as Edit } from '@lucide/svelte';

	export let user;
</script>

<div class="pt-6 pb-4">
	<div class="flex flex-col items-center sm:flex-row sm:items-start">
		<div class="relative mb-4 sm:mr-6 sm:mb-0">
			{#if user.avatar}
				<img
					src={user.avatar}
					alt={user.name}
					class="h-24 w-24 rounded-full border-2 border-white object-cover shadow-md"
				/>
			{:else}
				<div class="flex h-24 w-24 items-center justify-center rounded-full bg-gray-200">
					<User size={32} class="text-gray-400" />
				</div>
			{/if}
		</div>

		<div class="flex-1 text-center sm:text-left">
			<h1 class="mb-1 text-xl font-bold">{user.name}</h1>
			<p class="mb-3 text-sm text-gray-500">{user.bio}</p>

			<div class="mb-4 flex justify-center space-x-6 text-sm text-gray-600 sm:justify-start">
				<div>
					<span class="font-semibold">{user.followers}</span> 关注者
				</div>
				<div>
					<span class="font-semibold">{user.following}</span> 正在关注
				</div>
			</div>

			<div class="flex space-x-3">
				<button class="btn btn-primary px-4 py-1.5 text-sm">
					<Edit size={16} class="mr-1" /> 编辑资料
				</button>
				<button class="btn btn-outline px-4 py-1.5 text-sm">
					<Settings size={16} class="mr-1" /> 设置
				</button>
			</div>
		</div>
	</div>
</div>
