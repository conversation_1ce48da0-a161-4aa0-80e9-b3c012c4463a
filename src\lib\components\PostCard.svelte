<script lang="ts">
	import { goto } from '$app/navigation';
	import { Heart, MessageSquare } from '@lucide/svelte';
	import UserAvatar from './UserAvatar.svelte';

	export let post;

	function viewPost() {
		goto(`/post/${post.id}`);
	}
</script>

<div class="card group mb-4 cursor-pointer" onclick={viewPost}>
	<div class="relative overflow-hidden {heightClass}">
		<img
			src={post.imageUrl}
			alt={post.title}
			class="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
			loading="lazy"
		/>

		<div
			class="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black/60 to-transparent p-3 text-white"
		>
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-1">
					<Heart size={16} class="text-white" />
					<span class="text-xs">{post.likes}</span>
				</div>

				<div class="flex items-center space-x-1">
					<MessageSquare size={16} class="text-white" />
					<span class="text-xs">{post.comments.length}</span>
				</div>
			</div>
		</div>
	</div>

	<div class="p-3">
		<h3 class="group-hover:text-primary-600 mb-2 line-clamp-2 text-sm font-medium">
			{post.title}
		</h3>

		<div class="mt-2 flex items-center">
			<UserAvatar user={post.user} size="sm" />
			<span class="ml-2 truncate text-xs text-gray-500">{post.user.name}</span>
		</div>
	</div>
</div>
