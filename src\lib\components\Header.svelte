<script lang="ts">
	import { Home, Search, User, PlusSquare, Bell } from '@lucide/svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';

	export let showHeader = true;

	const navLinks = [
		{ href: '/', icon: Home, label: '首页' },
		{ href: '/search', icon: Search, label: '搜索' },
		{ href: '/create', icon: PlusSquare, label: '发布' },
		{ href: '/notifications', icon: Bell, label: '通知' },
		{ href: '/profile', icon: User, label: '我的' }
	];

	$: currentPath = $page.url.pathname;

	function handleNavigation(href: string) {
		goto(href);
	}
</script>

<header
	class="fixed top-0 right-0 left-0 z-50 bg-white transition-transform duration-300"
	style="transform: translateY({showHeader ? '0' : '-100%'})"
>
	<div class="container-custom">
		<div class="flex items-center justify-between py-3">
			<!-- Logo -->
			<div class="flex items-center">
				<h1 class="text-primary-600 text-xl font-bold">小红书</h1>
			</div>

			<!-- Search input (desktop) -->
			<div class="mx-8 hidden max-w-md flex-1 md:block">
				<div class="relative">
					<input
						type="text"
						placeholder="搜索你感兴趣的内容"
						class="focus:ring-primary-500 w-full rounded-full bg-gray-100 px-4 py-2 pl-10 text-sm focus:ring-2 focus:outline-none"
					/>
					<Search
						size={16}
						class="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-500"
					/>
				</div>
			</div>

			<!-- Desktop nav -->
			<nav class="hidden items-center space-x-6 md:flex">
				{#each navLinks as link}
					<a
						href={link.href}
						class="flex flex-col items-center text-xs font-medium {currentPath === link.href
							? 'text-primary-500'
							: 'text-gray-500 hover:text-gray-800'}"
					>
						<svelte:component this={link.icon} size={20} />
						<span>{link.label}</span>
					</a>
				{/each}
			</nav>
		</div>
	</div>

	<!-- Mobile bottom navigation -->
	<nav class="fixed right-0 bottom-0 left-0 z-50 border-t border-gray-200 bg-white md:hidden">
		<div class="flex justify-around">
			{#each navLinks as link}
				<button
					on:click={() => handleNavigation(link.href)}
					class="flex flex-col items-center px-3 py-2 {currentPath === link.href
						? 'text-primary-500'
						: 'text-gray-500'}"
				>
					<svelte:component this={link.icon} size={20} />
					<span class="mt-1 text-xs">{link.label}</span>
				</button>
			{/each}
		</div>
	</nav>
</header>

<!-- Bottom spacing for mobile navigation -->
<div class="h-16 md:hidden"></div>
